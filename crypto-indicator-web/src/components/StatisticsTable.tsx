import React, {use<PERSON><PERSON>back, useEffect, useMemo, useState} from "react";
import type {CryptoCurrencyStatisticsDto} from "../generated";
import {CryptoIndicatorApiClient} from "../generated";

// Formatting functions for different types of cryptocurrency data
const formatPrice = (value?: number): string => {
    if (!value) return "-";

    // For very small numbers (< 0.01), use significant digits to avoid showing 0
    if (value < 0.01) {
        return value.toLocaleString("en-US", {
            maximumSignificantDigits: 6,
            minimumSignificantDigits: 1,
        });
    }

    // For larger numbers, use standard decimal formatting
    if (value < 1) {
        return value.toLocaleString("en-US", {
            minimumFractionDigits: 4,
            maximumFractionDigits: 4,
        });
    }

    // For numbers >= 1, use appropriate decimal places
    return value.toLocaleString("en-US", {
        minimumFractionDigits: 2,
        maximumFractionDigits: 6,
    });
};

const formatMarketCap = (value?: number): string => {
    if (!value) return "-";

    // Format large numbers with appropriate suffixes
    if (value >= 1e12) {
        return `${(value / 1e12).toLocaleString("en-US", {
            maximumFractionDigits: 2,
        })}T`;
    }
    if (value >= 1e9) {
        return `${(value / 1e9).toLocaleString("en-US", {
            maximumFractionDigits: 2,
        })}B`;
    }
    if (value >= 1e6) {
        return `${(value / 1e6).toLocaleString("en-US", {
            maximumFractionDigits: 2,
        })}M`;
    }

    return value.toLocaleString("en-US", {maximumFractionDigits: 0});
};

const formatTechnicalIndicator = (value?: number): string => {
    // Technical indicators like SMMA use the same formatting as prices
    return formatPrice(value);
};

const formatDate = (isoString?: string) =>
    isoString ? new Date(isoString).toLocaleDateString() : "-";
const getSignalClass = (color?: string) =>
    `signal-badge signal-${color || "gray"}`;
const getEmoji = (color?: string) =>
    ({gold: "🟡", green: "🟢", blue: "🔵", red: "🔴", gray: "⚪"})[
    color || ""
        ] || "⚫";

const StatisticsTable: React.FC = () => {
    const [statistics, setStatistics] = useState<CryptoCurrencyStatisticsDto[]>([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const client = useMemo(() => new CryptoIndicatorApiClient(), []);

    const fetchData = useCallback(async () => {
        setLoading(true);
        setError(null);
        try {
            const result = await client.CryptoStatisticsController_getCryptoStatistics();
            setStatistics(result);
        } catch (err) {
            setError(err instanceof Error ? err.message : "Failed to fetch data");
        } finally {
            setLoading(false);
        }
    }, [client]);

    useEffect(() => {
        fetchData();
        const interval = setInterval(fetchData, 30000);
        return () => clearInterval(interval);
    }, [fetchData]);

    if (loading && statistics.length === 0) {
        return (
            <div className="app-container">
                <div className="header">
                    <h1>🚀 Crypto Indicator Dashboard</h1>
                    <p>Real-time cryptocurrency technical analysis</p>
                </div>
                <div className="loading-container">
                    <h3>Loading...</h3>
                </div>
            </div>
        );
    }

    if (error) {
        return (
            <div className="app-container">
                <div className="header">
                    <h1>🚀 Crypto Indicator Dashboard</h1>
                    <p>Real-time cryptocurrency technical analysis</p>
                </div>
                <div className="loading-container">
                    <h3>Error: {error}</h3>
                    <button onClick={fetchData}>Retry</button>
                </div>
            </div>
        );
    }

    const usdStatistics = statistics.filter((crypto) => crypto.conversionCurrency === "USD");
    const btcStatistics = statistics.filter((crypto) => crypto.conversionCurrency === "BTC");

    return (
        <div className="app-container">
            <div className="header">
                <h1>🚀 Crypto Indicator Dashboard</h1>
                <p>Real-time cryptocurrency technical analysis</p>
            </div>

            <div className="table-container">
                <div className="stats-info">
                    <div>
                        <strong>{statistics.length}</strong> cryptocurrencies
                    </div>
                    <button onClick={fetchData} disabled={loading}>
                        {loading ? "Refreshing..." : "Refresh"}
                    </button>
                </div>

                <div style={{overflowX: "auto"}}>
                    <table className="table">
                        <thead>
                        <tr>
                            <th>Cryptocurrency</th>
                            <th>USD Price</th>
                            <th>Market Cap</th>
                            <th>Last Update</th>
                            <th>USD Signal</th>
                            <th>SMMA-29 (USD)</th>
                            <th>BTC Price</th>
                            <th>BTC Signal</th>
                            <th>SMMA-29 (BTC)</th>
                        </tr>
                        </thead>
                        <tbody>
                        {usdStatistics.map((crypto: CryptoCurrencyStatisticsDto) => {
                            const usdData = crypto.indicatorValues.find((el) => el);
                            const btcData: any = btcStatistics.find((el) => el.symbol === crypto.symbol)?.indicatorValues?.find((el) => el);

                            return (
                                <tr key={crypto.symbol}>
                                    <td>
                                        <div className="symbol-cell">
                                            <div className="crypto-icon">
                                                {crypto?.symbol?.slice(0, 2)}
                                            </div>
                                            <strong>{crypto.symbol}</strong>
                                        </div>
                                    </td>
                                    <td>
                                        <strong>${formatPrice(usdData?.close)}</strong>
                                    </td>
                                    <td>
                                        <strong>${formatMarketCap(usdData?.marketCap)}</strong>
                                    </td>
                                    <td>{formatDate(usdData?.timestamp)}</td>
                                    <td>
                      <span className={getSignalClass(usdData?.color)}>
                        {getEmoji(usdData?.color)} {usdData?.color || "N/A"}
                      </span>
                                    </td>
                                    <td>${formatTechnicalIndicator(usdData?.smma_29)}</td>
                                    <td>
                                        <strong>{formatPrice(btcData?.close)} BTC</strong>
                                    </td>
                                    <td>
                      <span className={getSignalClass(btcData?.color)}>
                        {getEmoji(btcData?.color)} {btcData?.color || "N/A"}
                      </span>
                                    </td>
                                    <td>{formatTechnicalIndicator(btcData?.smma_29)} BTC</td>
                                </tr>
                            );
                        })}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    );
};

export default StatisticsTable;
