body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.app-container {
  min-height: 100vh;
  padding: 20px;
}

.header {
  text-align: center;
  margin-bottom: 40px;
}

.header h1 {
  font-size: 3rem;
  color: white;
  margin: 0 0 10px 0;
}

.header p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.2rem;
  margin: 0;
}

.table-container {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.stats-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: rgba(52, 73, 94, 0.05);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.stats-info button {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  margin-left: 15px;
}

.stats-info button:hover:not(:disabled) {
  background: #2563eb;
}

.stats-info button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
}

.table th {
  background: #2c3e50;
  color: white;
  padding: 15px;
  text-align: center;
  text-transform: uppercase;
}

.table td {
  padding: 15px;
  text-align: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.table tr:hover {
  background: rgba(102, 126, 234, 0.05);
}

.table tr:nth-child(even) {
  background: rgba(0, 0, 0, 0.02);
}

.rank-badge {
  background: #f39c12;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: bold;
  font-size: 12px;
  min-width: 30px;
  display: inline-block;
}

.crypto-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #667eea;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 12px;
  margin-right: 10px;
}

.symbol-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.signal-badge {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: bold;
  font-size: 12px;
  text-transform: uppercase;
}

.signal-gold { background: #fff3cd; color: #b7791f; }
.signal-green { background: #d4edda; color: #00b894; }
.signal-blue { background: #cce7ff; color: #0984e3; }
.signal-red { background: #f8d7da; color: #d63031; }
.signal-gray { background: #f8f9fa; color: #636e72; }

.loading-container {
  text-align: center;
  padding: 60px 40px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  margin: 40px auto;
  max-width: 600px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .stats-info {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .table th, .table td {
    padding: 12px 8px;
    font-size: 12px;
  }

  .header h1 {
    font-size: 2rem;
  }
}
